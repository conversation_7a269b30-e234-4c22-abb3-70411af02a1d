import requests
import json
import os
import re

def handler(pd: "pipedream"):
    """
    更新 pushwoosh 的 metrics 数据到飞书
    """
    app_id = "cli_a50254e4e43bd013"
    app_secret = "0PStyxmXLUnUKq5KvS5iCbmg8z4z6nkJ"
    result_token = get_tenant_access_token(app_id, app_secret)
    if result_token.get("code") == 0:
        access_token = result_token.get("tenant_access_token")
        print(access_token)
        
        # 示例：处理metrics数据并更新飞书记录
        # app_id和table_id需要根据实际情况替换
        feishu_app_id = "GG47btrdTae8lhs2TapcihNnnRh"
        feishu_table_id = "tblwMFPaCuXBxNUu"
        metrics_data = pd.json_string(pd.env.get("metrics_result"))
        process_metrics_data(metrics_data, access_token, feishu_app_id, feishu_table_id)
        return {"message": "Metrics data updated to Feishu successfully"}
    else:
        print(f"获取飞书租户访问凭证失败: {result_token.get('msg')}")
        return {"message": f"获取飞书租户访问凭证失败: {result_token.get('msg')}"}


def get_tenant_access_token(app_id: str, app_secret: str) -> dict:
    """
    获取飞书租户访问凭证
    
    Args:
        app_id (str): 飞书应用的 App ID
        app_secret (str): 飞书应用的 App Secret
        
    Returns:
        dict: 包含访问凭证的响应数据
    """
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    
    response = requests.post(url, headers=headers, json=payload)
    return response.json()


def search_records(access_token: str, app_id: str, table_id: str, conditions: list, conjunction: str = "and", page_size: int = 10) -> dict:
    """
    搜索多维表格中的记录
    
    Args:
        access_token (str): 飞书访问凭证
        app_id (str): 多维表格应用ID
        table_id (str): 表格ID
        conditions (list): 查询条件列表，每个条件应为包含field_name、operator和value的字典
        conjunction (str): 条件连接方式，可为"and"或"or"，默认为"and"
        page_size (int): 每页记录数，默认为10
        
    Returns:
        dict: 包含查询结果的响应数据
    """
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_id}/tables/{table_id}/records/search"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "filter": {
            "conditions": conditions,
            "conjunction": conjunction
        },
        "page_size": page_size
    }
    
    response = requests.post(url, headers=headers, json=payload, params={"page_size": page_size})
    return response.json()


def update_record(access_token: str, app_id: str, table_id: str, record_id: str, fields: dict) -> dict:
    """
    更新多维表格中的记录
    
    Args:
        access_token (str): 飞书访问凭证
        app_id (str): 多维表格应用ID
        table_id (str): 表格ID
        record_id (str): 记录ID
        fields (dict): 要更新的字段，键为字段名，值为新的字段值
        
    Returns:
        dict: 包含更新结果的响应数据
    """
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_id}/tables/{table_id}/records/{record_id}"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "fields": fields
    }
    
    response = requests.put(url, headers=headers, json=payload)
    return response.json()


def process_metrics_data(metrics_data, access_token: str, app_id: str, table_id: str) -> None:
    """
    处理指标数据，查询飞书记录并更新
    
    Args:
        json_file_path (str): metrics_result.json文件的路径
        access_token (str): 飞书访问凭证
        app_id (str): 多维表格应用ID
        table_id (str): 表格ID
    """
    try:
        # 读取JSON文件
        with open(metrics_data, 'r', encoding='utf-8') as file:
            metrics_data = json.load(file)
        
        # 遍历所有项目
        for project_code, project_items in metrics_data.items():
            print(f"处理项目: {project_code}")
            
            for item in project_items:
                for title, metrics in item.items():
                    # 去掉标题中的 emoji
                    # 更精确的emoji移除方法
                    emoji_pattern = re.compile(
                        "["
                        "\U0001F1E0-\U0001F1FF"  # 国旗 (flags)
                        "\U0001F300-\U0001F5FF"  # 符号与象形文字
                        "\U0001F600-\U0001F64F"  # 表情
                        "\U0001F680-\U0001F6FF"  # 交通与地图
                        "\U0001F700-\U0001F77F"  # 字母符号
                        "\U0001F780-\U0001F7FF"  # 几何符号
                        "\U0001F800-\U0001F8FF"  # 补充箭头
                        "\U0001F900-\U0001F9FF"  # 补充符号和象形文字
                        "\U0001FA00-\U0001FA6F"  # 棋类符号
                        "\U0001FA70-\U0001FAFF"  # 符号和象形文字扩展
                        "\U00002702-\U000027B0"  # 装饰符号
                        "\U000024C2-\U0000257F"  # 封闭字母数字
                        "\U00002600-\U000026FF"  # 杂项符号
                        "\U00002700-\U000027BF"  # 装饰符号
                        "\U0000FE00-\U0000FE0F"  # 变体选择器
                        "\U0001F900-\U0001F9FF"  # 补充符号和象形文字
                        "]+", flags=re.UNICODE)
                    
                    title = emoji_pattern.sub(r'', title)
                    # 移除 title 结尾的空格
                    title = title.rstrip(' ')
                    import string
                    punctuation_to_remove = string.punctuation
                    title = title.rstrip(punctuation_to_remove)

                    title = title.rstrip(__import__('string').punctuation + "，。、；？！（）：【】《》“”‘’…—～·" + __import__('string').whitespace)

                    # 移除 title 最后一个字符
                    title = title[:-1]

                    print(f"优化后的标题:\n{title}")
                    conditions = [
                        {
                            "field_name": "Title",
                            "operator": "contains",
                            "value": [title]
                        },
                        {
                            "field_name": "Project",
                            "operator": "is",
                            "value": [project_code]
                        }
                    ]
                    
                    # 查询记录
                    search_result = search_records(access_token, app_id, table_id, conditions)
                    
                    if search_result.get("code") == 0 and search_result.get("data", {}).get("items"):
                        # 找到匹配记录
                        records = search_result["data"]["items"]
                        for record in records:
                            record_id = record["record_id"]
                            
                            # 准备要更新的数据
                            update_data = {
                                "Data": f"Recipients: {metrics['recipients']}\nOpened: {metrics['opened']}\nOpen rate: {metrics['ctr']}%"
                            }
                            
                            # 更新记录
                            update_result = update_record(access_token, app_id, table_id, record_id, update_data)
                            
                            if update_result.get("code") == 0:
                                print(f"  成功更新记录 - 项目: {project_code}, 标题: {title}")
                            else:
                                print(f"  更新记录失败 - 项目: {project_code}, 标题: {title}, 错误: {update_result.get('msg')}")
                    else:
                        # 未找到匹配记录
                        print(f"  未找到记录 - 项目: {project_code}, 标题: {title}, 跳过更新")
        
        print("处理完成")
    except Exception as e:
        print(f"处理metrics数据时出错: {str(e)}")


if __name__ == "__main__":
    app_id = "cli_a50254e4e43bd013"
    app_secret = "0PStyxmXLUnUKq5KvS5iCbmg8z4z6nkJ"
    result_token = get_tenant_access_token(app_id, app_secret)
    if result_token.get("code") == 0:
        access_token = result_token.get("tenant_access_token")
        print(access_token)
        
        # 示例：处理metrics数据并更新飞书记录
        # app_id和table_id需要根据实际情况替换
        feishu_app_id = "GG47btrdTae8lhs2TapcihNnnRh"
        feishu_table_id = "tblwMFPaCuXBxNUu"
        process_metrics_data("test.json", access_token, feishu_app_id, feishu_table_id)
    else:
        print(f"获取飞书租户访问凭证失败: {result_token.get('msg')}")
