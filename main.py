import requests
import json
from typing import Dict, Any, Optional, List
from tqdm import tqdm


def handler(pd: "pipedream"):
    """
    获取 pushwoosh 的 metrics 数据
    """
    all_metrics = extract_metrics_by_project(AUTH_TOKEN)
    return all_metrics

class PushwooshAPI:
    """Pushwoosh API客户端类"""
    
    BASE_URL = "https://app.pushwoosh.com/api/v2"
    
    def __init__(self, auth_token: Optional[str] = None):
        """
        初始化Pushwoosh API客户端
        
        Args:
            auth_token: 授权令牌，如果为None则需要在调用方法时提供
        """
        self.auth_token = auth_token
        self.session = requests.Session()
    
    def _get_headers(self, auth_token: Optional[str] = None) -> Dict[str, str]:
        """
        获取请求头
        
        Args:
            auth_token: 授权令牌，如果为None则使用实例的auth_token
            
        Returns:
            包含请求头信息的字典
        """
        token = auth_token or self.auth_token
        if not token:
            raise ValueError("授权令牌不能为空")
            
        return {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'authorization': f'Bearer {token}',
            'content-type': 'application/json',
            'origin': 'https://app.pushwoosh.com',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
        }
    
    def get_message_history(
        self, 
        application_code: str, 
        page: int = 1, 
        limit: int = 20, 
        auth_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取应用的消息历史
        
        Args:
            application_code: 应用代码
            page: 页码，默认为1
            limit: 每页限制数量，默认为20
            auth_token: 授权令牌，如果为None则使用实例的auth_token
            
        Returns:
            API响应的JSON数据
        """
        url = f"{self.BASE_URL}/statistics/application/getMessageHistory"
        
        # 准备请求数据
        data = {
            "application_code": application_code,
            "page": str(page),
            "limit": str(limit)
        }
        
        # 发送请求
        response = self.session.post(
            url=url,
            headers=self._get_headers(auth_token),
            json=data
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        return response.json()


def get_pushwoosh_message_history(
    application_code: str, 
    auth_token: str, 
    page: int = 1, 
    limit: int = 20
) -> Dict[str, Any]:
    """
    获取Pushwoosh应用的消息历史的独立函数
    
    Args:
        application_code: 应用代码
        auth_token: 授权令牌
        page: 页码，默认为1
        limit: 每页限制数量，默认为20
        
    Returns:
        API响应的JSON数据
    """
    api = PushwooshAPI(auth_token)
    return api.get_message_history(application_code, page, limit)


def extract_metrics_by_project(
    auth_token: str,
) -> Dict[str, List[Dict]]:
    """
    提取所有项目的消息历史metrics信息并按项目编号整理
    
    Args:
        auth_token: 授权令牌
        project_codes_file: 项目编号与应用代码映射的JSON文件路径
        
    Returns:
        按项目编号整理的metrics信息
    """
    # # 加载项目编号与应用代码映射
    # with open(project_codes_file, 'r') as f:
    #     project_codes = json.load(f)
    project_codes = {
        "NOTM105": "09F8F-B8B1C",
        "NOTM201": "7D5F2-AFA33",
        "OOG101": "6219B-53250",
        "OOG102": "300B9-F9B1D",
        "OOG105": "4C3C9-652B5",
        "OOG106": "6F32D-89691",
        "OOG109": "CDC28-0B8AC",
        "OOG111": "EA6DC-DD39D",
        "OOG113": "A62B0-F85FF",
        "OOG116": "6DCBA-1FEAD",
        "OOG118": "15DB1-1F332",
        "OOG120": "9B17F-108E6",
        "OOG121": "AC7BC-9C2FF",
        "OOG123": "C6CF1-E7241",
        "OOG202": "CF249-1604E",
        "OOG206": "0A709-9A266"
    }
    # 创建API客户端
    api_client = PushwooshAPI(auth_token)
    
    # 存储结果的字典
    results = {}
    
    # 反向映射应用代码到项目编号
    app_code_to_project = {v: k for k, v in project_codes.items()}
    
    # 创建进度条
    total_projects = len(project_codes)
    print(f"开始处理 {total_projects} 个项目的消息历史...")
    
    # 遍历每个项目，使用tqdm显示进度
    for project_id, app_code in tqdm(project_codes.items(), total=total_projects, desc="处理项目进度"):
        try:
            # 获取消息历史
            history_data = api_client.get_message_history(app_code)
            
            # 初始化项目的结果列表
            results[project_id] = []
            
            # 提取消息中的metrics信息
            if "items" in history_data:
                for message in history_data.get("items", []):
                    if message.get("status") == "MESSAGE_STATUS_DONE":
                        title = message.get("title", "无标题")
                        metrics = message.get("metrics", {})
                        
                        # 仅保留需要的metrics字段
                        metrics_data = {
                            "recipients": int(metrics.get("recipients", 0)),
                            "opened": int(metrics.get("opened", 0)),
                            "ctr": metrics.get("ctr", 0)
                        }
                        
                        # 添加到结果列表
                        results[project_id].append({title: metrics_data})
        except Exception as e:
            print(f"获取项目 {project_id} 的数据时出错: {str(e)}")
    
    print("所有项目处理完成！")
    return results


def save_metrics_to_json(metrics_data: Dict[str, List[Dict]], output_file: str = "metrics_result.json"):
    """
    将提取的metrics数据保存为JSON文件
    
    Args:
        metrics_data: 提取的metrics数据
        output_file: 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(metrics_data, f, ensure_ascii=False, indent=2)
    
    print(f"已将metrics数据保存到 {output_file}")


# 使用示例
if __name__ == "__main__":
    # 示例参数
    AUTH_TOKEN = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.je91OduzR4vcPrB54kYQHhLtUIGdoss_lkOEBGEFa51zR3dMqpMX2muRbfPqlmpMcvyBhjxO2yKIcJosRE1XpA"
    
    # # 获取单个项目的消息历史（示例）
    # project_id = "OOG113"
    # app_code = "A62B0-F85FF"
    
    # # 方法1: 获取单个项目的消息历史
    # api_client = PushwooshAPI(AUTH_TOKEN)
    # result = api_client.get_message_history(app_code)
    
    # 打印结果
    # print(json.dumps(result, indent=2))
    
    # 方法2: 提取所有项目的metrics信息并整理
    all_metrics = extract_metrics_by_project(AUTH_TOKEN)
    
    # 保存结果到JSON文件
    save_metrics_to_json(all_metrics)
